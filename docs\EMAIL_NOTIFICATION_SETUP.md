# 智能邮件通知系统设置指南

## 功能概述

本系统实现了基于用户IP地址的智能通知功能：
- **中国大陆IP**：发送短信通知
- **非中国大陆IP**：发送邮件通知
- **邮件发送失败时**：自动回退到短信通知

## 1. 数据库设置

### 1.1 添加邮箱字段到用户表

运行以下命令添加email字段到plc_user表：

```bash
php artisan migrate
```

这将执行 `2024_01_01_000000_add_email_to_plc_user_table.php` migration文件。

### 1.2 手动添加邮箱字段（如果migration失败）

```sql
ALTER TABLE `plc_user` ADD COLUMN `email` VARCHAR(255) NULL COMMENT '用户邮箱地址' AFTER `phone`;
```

## 2. Gmail SMTP配置

### 2.1 获取Gmail应用密码

1. 登录您的Gmail账户
2. 前往 [Google账户设置](https://myaccount.google.com/)
3. 选择"安全性" → "两步验证"（必须先启用两步验证）
4. 选择"应用密码"
5. 选择"邮件"和您的设备
6. 生成16位应用密码

### 2.2 配置.env文件

在项目根目录的`.env`文件中添加以下配置：

```env
# 邮件配置 - Gmail SMTP
MAIL_DRIVER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-16-digit-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="设备监控系统"
```

**注意**：
- `MAIL_USERNAME` 和 `MAIL_FROM_ADDRESS` 使用您的Gmail地址
- `MAIL_PASSWORD` 使用16位应用密码，不是Gmail登录密码
- 不要在应用密码中包含空格

## 3. 测试功能

### 3.1 测试邮件配置

访问以下URL测试邮件配置：

```
GET /test/mail-config
```

### 3.2 测试邮件发送

```
POST /test/email
Content-Type: application/json

{
    "email": "<EMAIL>"
}
```

### 3.3 测试设备报警邮件

```
POST /test/device-alert
Content-Type: application/json

{
    "email": "<EMAIL>",
    "device_name": "TEST_DEVICE_001",
    "alert_content": "测试报警信息"
}
```

### 3.4 测试IP地理位置检测

```
GET /test/ip-location?ip=*******
```

### 3.5 测试智能通知

```
POST /test/smart-notification
Content-Type: application/json

{
    "phone": "13800138000",
    "email": "<EMAIL>",
    "device_name": "TEST_DEVICE_001",
    "alert_content": "测试报警信息",
    "test_ip": "*******"
}
```

## 4. 使用方法

### 4.1 在现有代码中使用

原有的通知代码已经自动升级为智能通知。在 `MqttController` 中，原来的：

```php
$the->sendCode(['phone' => $phone, 'devicename' => $devicename, 'contents' => $contents]);
```

已经替换为：

```php
$notificationData = [
    'phone' => $phone, 
    'email' => $email,
    'devicename' => $devicename, 
    'contents' => $contents
];
$the->sendSmartNotification($notificationData);
```

### 4.2 手动调用智能通知

```php
use App\Http\Controllers\Home\CodeController;

$codeController = new CodeController();
$notificationData = [
    'phone' => '13800138000',
    'email' => '<EMAIL>',
    'devicename' => 'DEVICE_001',
    'contents' => '设备报警信息'
];

$result = $codeController->sendSmartNotification($notificationData);
```

## 5. 邮件内容自定义

邮件内容在 `app/Services/EmailNotificationService.php` 的 `sendDeviceAlert` 方法中定义。

您可以修改该方法中的 `$message` 变量来自定义邮件内容。邮件采用纯文本格式，简洁高效。

## 6. 日志和调试

### 6.1 查看日志

系统会记录以下信息到Laravel日志中：
- IP地理位置检测结果
- 邮件发送成功/失败
- 智能通知决策过程

日志文件位置：`storage/logs/laravel.log`

### 6.2 常见问题

**邮件发送失败**：
1. 检查Gmail应用密码是否正确
2. 确认Gmail账户已启用两步验证
3. 检查防火墙是否阻止SMTP连接
4. 查看Laravel日志获取详细错误信息

**IP检测不准确**：
1. 系统使用免费的ip-api.com服务
2. 内网IP默认认为是中国大陆
3. 可以在测试时指定test_ip参数

## 7. 安全注意事项

1. **保护应用密码**：不要将Gmail应用密码提交到版本控制系统
2. **使用环境变量**：所有敏感配置都应通过.env文件管理
3. **定期更新密码**：建议定期更换Gmail应用密码
4. **监控使用情况**：定期检查Gmail的安全活动

## 8. 扩展功能

### 8.1 支持其他邮件服务商

可以修改配置支持其他SMTP服务：

**QQ邮箱**：
```env
MAIL_HOST=smtp.qq.com
MAIL_PORT=587
MAIL_ENCRYPTION=tls
```

**163邮箱**：
```env
MAIL_HOST=smtp.163.com
MAIL_PORT=994
MAIL_ENCRYPTION=ssl
```

### 8.2 添加更多通知方式

可以扩展 `sendSmartNotification` 方法支持：
- 微信通知
- 钉钉通知
- Slack通知
- 企业微信通知

## 9. 性能优化

1. **队列处理**：对于大量邮件，建议使用Laravel队列
2. **缓存IP信息**：可以缓存IP地理位置信息减少API调用
3. **批量发送**：支持批量发送邮件功能

## 10. 维护和监控

1. **定期测试**：建议每月测试一次邮件发送功能
2. **监控发送量**：注意Gmail的发送限制
3. **备份配置**：定期备份邮件配置和模板
