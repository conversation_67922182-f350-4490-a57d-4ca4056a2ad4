<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Api\Controller;
use App\Http\Controllers\Home\CodeController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Log;
use App\Services\AlertMessageService;

class MqttController extends Controller
{

    public function mqtt(Request $request)
    {
        $data = $request->json()->all();
        $base64str = $data['payload'];
        $devicename = $data['from_client_id']; //设备唯一编号
        $bytedata = base64_decode($base64str);

        DB::table('plc_device_user')->where('device_number', $devicename)->update(['time' => time(), 'status' => 0]);

        //处理长时间未运行报警
        $device_id = DB::table("plc_device_user")->where('device_number', $devicename)->value('id');
        DB::table('plc_device_error')->where('u_id', $device_id)->where('c_id', 1)->where('has_sovle', 0)->update(['has_sovle' => 1]);
        if (!empty($devicename)) {
            $online_info = DB::table('plc_online')->where('device_number', $devicename)->orderBy('id', 'desc')->first();
            if (empty($online_info)) {
                DB::table('plc_online')->insert([
                    'device_number' => $devicename,
                    'starttime' => time(),
                    'endtime' => time(),
                ]);
            } else {
                $endtime = $online_info->endtime;
                if (time() - $endtime > 99) {
                    DB::table('plc_online')->insert([
                        'device_number' => $devicename,
                        'starttime' => time(),
                        'endtime' => time(),
                    ]);
                } else {
                    DB::table('plc_online')->where('id', $online_info->id)->update(['endtime' => time()]);
                }
            }
        }

        $messagelen = strlen($bytedata);
        $message_hex = "";
        for ($i = 0; $i < $messagelen; $i++) {
            $message_hex .= sprintf("%02X", ord($bytedata[$i]));
        }
        // Log::info('tt---------'.$message_hex);
        if (empty($bytedata) || $messagelen < 200) {
            return 123;
        } else {

            $scf = ord($bytedata[3]) << 8 | ord($bytedata[4]); //首次冲洗分
            $scm = ord($bytedata[5]) << 8 | ord($bytedata[6]); //首次冲洗秒
            $zcf = ord($bytedata[7]) << 8 | ord($bytedata[8]); //正常冲洗分
            $zcm = ord($bytedata[9]) << 8 | ord($bytedata[10]); //正常冲洗秒
            $jsf = ord($bytedata[11]) << 8 | ord($bytedata[12]); //加水时间分
            $jsm = ord($bytedata[13]) << 8 | ord($bytedata[14]); //加水时间秒
            $jyf = ord($bytedata[15]) << 8 | ord($bytedata[16]); //加药时间分
            $jym = ord($bytedata[17]) << 8 | ord($bytedata[18]); //加药时间秒
            $qxs = ord($bytedata[19]) << 8 | ord($bytedata[20]); //清洗等待时间时
            $qxf = ord($bytedata[21]) << 8 | ord($bytedata[22]); //清洗等待时间分
            $gys = ord($bytedata[23]) << 8 | ord($bytedata[24]); //感应器等待时间时
            $gyf = ord($bytedata[25]) << 8 | ord($bytedata[26]); //感应器等待时间分
            $gym = ord($bytedata[27]) << 8 | ord($bytedata[28]); //感应器延时检测秒
            $cxm = ord($bytedata[29]) << 8 | ord($bytedata[30]); //冲洗延时开启秒
            $psm = ord($bytedata[31]) << 8 | ord($bytedata[32]); //排水延时关闭秒
            $cxkm = ord($bytedata[33]) << 8 | ord($bytedata[34]); //冲洗气缸开时间秒
            $cxgm = ord($bytedata[35]) << 8 | ord($bytedata[36]); //冲洗气缸关时间秒
            $cd = ord($bytedata[37]) << 8 | ord($bytedata[38]); //长度
            $kd = ord($bytedata[39]) << 8 | ord($bytedata[40]); //宽度
            $gd = ord($bytedata[41]) << 8 | ord($bytedata[42]); //填充高度
            $zns = ord($bytedata[43]) << 8 | ord($bytedata[44]); //总牛数
            $dqns = ord($bytedata[45]) << 8 | ord($bytedata[46]); //当前牛数
            $nswc = ord($bytedata[47]) << 8 | ord($bytedata[48]); //牛数误差
            $hxns = ord($bytedata[49]) << 8 | ord($bytedata[50]); //换洗牛数
            $szn = ord($bytedata[51]) << 8 | ord($bytedata[52]); //设置年
            $szy = ord($bytedata[53]) << 8 | ord($bytedata[54]); //设置月
            $szr = ord($bytedata[55]) << 8 | ord($bytedata[56]); //设置日
            $szs = ord($bytedata[57]) << 8 | ord($bytedata[58]); //设置时
            $szf = ord($bytedata[59]) << 8 | ord($bytedata[60]); //设置分
            $szm = ord($bytedata[61]) << 8 | ord($bytedata[62]); //设置秒
            $szxq = ord($bytedata[63]) << 8 | ord($bytedata[64]); //设置星期
            $szbz = ord($bytedata[65]) << 8 | ord($bytedata[66]); //设置标志
            $dn = ord($bytedata[67]) << 8 | ord($bytedata[68]); //读年
            $dy = ord($bytedata[69]) << 8 | ord($bytedata[70]); //读月
            $dr = ord($bytedata[71]) << 8 | ord($bytedata[72]); //读日
            $ds = ord($bytedata[73]) << 8 | ord($bytedata[74]); //读时
            $df = ord($bytedata[75]) << 8 | ord($bytedata[76]); //读分
            $dm = ord($bytedata[77]) << 8 | ord($bytedata[78]); //读秒
            $dxq = ord($bytedata[79]) << 8 | ord($bytedata[80]); //读星期
            $bone = ord($bytedata[81]) << 8 | ord($bytedata[82]); //泵1星期一使用
            $btwo = ord($bytedata[83]) << 8 | ord($bytedata[84]); //泵1星期二使用
            $bthree = ord($bytedata[85]) << 8 | ord($bytedata[86]); //泵1星期三使用
            $bfour = ord($bytedata[87]) << 8 | ord($bytedata[88]); //泵1星期四使用
            $bfive = ord($bytedata[89]) << 8 | ord($bytedata[90]); //泵1星期五使用
            $bsix = ord($bytedata[91]) << 8 | ord($bytedata[92]); //泵1星期六使用
            $bseven = ord($bytedata[93]) << 8 | ord($bytedata[94]); //泵1星期天使用
            $bones = ord($bytedata[95]) << 8 | ord($bytedata[96]); //泵2星期一使用
            $btwos = ord($bytedata[97]) << 8 | ord($bytedata[98]); //泵2星期二使用
            $bthrees = ord($bytedata[99]) << 8 | ord($bytedata[100]); //泵2星期三使用
            $bfours = ord($bytedata[101]) << 8 | ord($bytedata[102]); //泵2星期四使用
            $bfives = ord($bytedata[103]) << 8 | ord($bytedata[104]); //泵2星期五使用
            $bsixs = ord($bytedata[105]) << 8 | ord($bytedata[106]); //泵2星期六使用
            $bsevens = ord($bytedata[107]) << 8 | ord($bytedata[108]); //泵2星期天使用
            $qdsA = ord($bytedata[109]) << 8 | ord($bytedata[110]); //启动时间小时1
            $qdfA = ord($bytedata[111]) << 8 | ord($bytedata[112]); //启动时间分钟1
            $qdsB = ord($bytedata[113]) << 8 | ord($bytedata[114]); //启动时间小时2
            $qdfB = ord($bytedata[115]) << 8 | ord($bytedata[116]); //启动时间分钟2
            $qdsC = ord($bytedata[117]) << 8 | ord($bytedata[118]); //启动时间小时3
            $qdfC = ord($bytedata[119]) << 8 | ord($bytedata[120]); //启动时间分钟3
            $qdsD = ord($bytedata[121]) << 8 | ord($bytedata[122]); //启动时间小时4
            $qdfD = ord($bytedata[123]) << 8 | ord($bytedata[124]); //启动时间分钟4
            $qdsE = ord($bytedata[125]) << 8 | ord($bytedata[126]); //启动时间小时5
            $qdfE = ord($bytedata[127]) << 8 | ord($bytedata[128]); //启动时间分钟5
            $qdsF = ord($bytedata[129]) << 8 | ord($bytedata[130]); //启动时间小时6
            $qdfF = ord($bytedata[131]) << 8 | ord($bytedata[132]); //启动时间分钟6
            $qdsG = ord($bytedata[133]) << 8 | ord($bytedata[134]); //启动时间小时7
            $qdfG = ord($bytedata[135]) << 8 | ord($bytedata[136]); //启动时间分钟7
            $qdsH = ord($bytedata[137]) << 8 | ord($bytedata[138]); //启动时间小时8
            $qdfH = ord($bytedata[139]) << 8 | ord($bytedata[140]); //启动时间分钟8
            $qdsI = ord($bytedata[141]) << 8 | ord($bytedata[142]); //启动时间小时9
            $qdfI = ord($bytedata[143]) << 8 | ord($bytedata[144]); //启动时间分钟9
            $qdsJ = ord($bytedata[145]) << 8 | ord($bytedata[146]); //启动时间小时10
            $qdfJ = ord($bytedata[147]) << 8 | ord($bytedata[148]); //启动时间分钟10
            $qdsK = ord($bytedata[149]) << 8 | ord($bytedata[150]); //启动时间小时11
            $qdfK = ord($bytedata[151]) << 8 | ord($bytedata[152]); //启动时间分钟11
            $qdsL = ord($bytedata[153]) << 8 | ord($bytedata[154]); //启动时间小时12
            $qdfL = ord($bytedata[155]) << 8 | ord($bytedata[156]); //启动时间分钟12
            $qdsM = ord($bytedata[157]) << 8 | ord($bytedata[158]); //启动时间小时13
            $qdfM = ord($bytedata[159]) << 8 | ord($bytedata[160]); //启动时间分钟13
            $qdsN = ord($bytedata[161]) << 8 | ord($bytedata[162]); //启动时间小时14
            $qdfN = ord($bytedata[163]) << 8 | ord($bytedata[164]); //启动时间分钟14
            $qdsO = ord($bytedata[165]) << 8 | ord($bytedata[166]); //启动时间小时15
            $qdfO = ord($bytedata[167]) << 8 | ord($bytedata[168]); //启动时间分钟15
            $qdsP = ord($bytedata[169]) << 8 | ord($bytedata[170]); //启动时间小时16
            $qdfP = ord($bytedata[171]) << 8 | ord($bytedata[172]); //启动时间分钟16
            $qdsQ = ord($bytedata[173]) << 8 | ord($bytedata[174]); //启动时间小时17
            $qdfQ = ord($bytedata[175]) << 8 | ord($bytedata[176]); //启动时间分钟17
            $qdsR = ord($bytedata[177]) << 8 | ord($bytedata[178]); //启动时间小时18
            $qdfR = ord($bytedata[179]) << 8 | ord($bytedata[180]); //启动时间分钟18
            $qdsS = ord($bytedata[181]) << 8 | ord($bytedata[182]); //启动时间小时19
            $qdfS = ord($bytedata[183]) << 8 | ord($bytedata[184]); //启动时间分钟19
            $qdsT = ord($bytedata[185]) << 8 | ord($bytedata[186]); //启动时间小时20
            $qdfT = ord($bytedata[187]) << 8 | ord($bytedata[188]); //启动时间分钟20
            $zcq = ord($bytedata[189]) << 8 | ord($bytedata[190]); //自服务以来重启次数
            $ms = ord($bytedata[191]) << 8 | ord($bytedata[192]); //模式
            $jrcq = ord($bytedata[193]) << 8 | ord($bytedata[194]); //今日重启次数
            $bcyjc = ord($bytedata[195]) << 8 | ord($bytedata[196]); //本次已检测数量
            $bxz = ord($bytedata[197]) << 8 | ord($bytedata[198]); //泵选择
            $bstate = ord($bytedata[199]) << 8 | ord($bytedata[200]); //泵1状态
            $bstates = ord($bytedata[201]) << 8 | ord($bytedata[202]); //泵2状态

            $breset = ord($bytedata[203]) << 8 | ord($bytedata[204]); //启动按钮
            $pump1_action = ord($bytedata[205]) << 8 | ord($bytedata[206]); //泵1总运行次数
            $pump2_action = ord($bytedata[207]) << 8 | ord($bytedata[208]); //泵2总运行次数
            $fenshan = ord($bytedata[209]) << 8 | ord($bytedata[210]);//风扇同步
            $slgyq = ord($bytedata[211]) << 8 | ord($bytedata[212]); //数量感应器
            $waterPump = ord($bytedata[213]) << 8 | ord($bytedata[214]); //水泵(0 停止1运行 2故障)
            $csjwyxbj = ord($bytedata[217]) << 8 | ord($bytedata[218]); //长时间未运行报警(1 报警 0 正常)
            $leveld = ord($bytedata[219]) << 8 | ord($bytedata[220]); //泵1液位低
            $levelsd = ord($bytedata[223]) << 8 | ord($bytedata[224]); //泵2液位低
            $yld = ord($bytedata[227]) << 8 | ord($bytedata[228]); //压力低
            $programError = ord($bytedata[229]) << 8 | ord($bytedata[230]); //程序执行异常
            // $pumpll = ord($bytedata[231]) << 8 | ord($bytedata[232]); //泵1流量设定(M3/H 系统根据流量计算药剂累计使用量)
            // $pumplls = ord($bytedata[233]) << 8 | ord($bytedata[234]); //泵2流量设定(M3/H 系统根据流量计算药剂累计使用量)
            $weiyunxingsjsz = ord($bytedata[235]) << 8 | ord($bytedata[236]); //未运行时间设置(0-32767H)
            $jyb = ord($bytedata[237]) << 8 | ord($bytedata[238]); //泵1加药百分数
            $jybs = ord($bytedata[239]) << 8 | ord($bytedata[240]); //泵2加药百分数
            $ben1base = ord($bytedata[241]) << 8 | ord($bytedata[242]); //泵1基数
            $ben1water = ord($bytedata[243]) << 8 | ord($bytedata[244]); //泵1加水时间（秒）
            $functionnumber = ord($bytedata[243]) << 8 | ord($bytedata[244]); //泵1加药时间（秒）
        }

        // if($devicename == 't10086'){
        //     logger()->info('洗牛数：'.$dqns);
        //     logger()->info('本次已检测数量：'.$bcyjc);
        //     logger()->info('换液数：'.$hxns);
        // }
        $noticeSN = DB::table('plc_error_notice')->where(['sn' => $devicename])->first();
        $initialInstallSN = DB::table('plc_initial_install')->where(['sn' => $devicename])->first();
        $detailsSN = DB::table('plc_device_details')->where(['sn' => $devicename])->first();
        $dateSN = DB::table('plc_date')->where(['sn' => $devicename])->get();
        $timingSN = DB::table('plc_timing')->where(['sn' => $devicename])->get();
        $generalSN = DB::table('plc_general')->where(['sn' => $devicename])->first();
        $noticeSN = objToArr($noticeSN);
        $initialInstallSN = objToArr($initialInstallSN);
        $detailsSN = objToArr($detailsSN);
        $dateSN = objToArr($dateSN);
        $timingSN = objToArr($timingSN);
        $generalSN = objToArr($generalSN);
        // Log::info('风扇同步：'.$fenshan);
        //判断泵选择与上次是否一致,或者长宽高是否有变化,泵1按百分比计算加药时间 加药时间=（容量*百分比）/基数(0.035)/60
        if ($bxz) {
            if ($bxz == 1) {
                $addtime = ($initialInstallSN['rl'] * $generalSN['jyb'] * 0.01) / 0.24 / 60;
                $addtime = explode('.', $addtime);
                $min = $addtime[0];
                $sec = $addtime[1] ? intval(('0.'.$addtime[1])*60) : 0;
               
                if(($jyf != $min) || ($jym != $sec)) {
                    $data_ = [];
                    $data_[0]['clientid'] = $devicename;
                    $data_[0]['variable'] = $min;
                    $data_[0]['weizhi'] = 6;
                    $data_[1]['clientid'] = $devicename;
                    $data_[1]['variable'] = $sec;
                    $data_[1]['weizhi'] = 7;
                    $this->fasong_s($data_, 2, 1);
                }
            }

            if ($bxz == 2 && (($jyf != $initialInstallSN['jyf']) || ($jym != $initialInstallSN['jym']))) {
                $data_ = [];
                $data_[0]['clientid'] = $devicename;
                $data_[0]['variable'] = $initialInstallSN['jyf'];
                $data_[0]['weizhi'] = 6;
                $data_[1]['clientid'] = $devicename;
                $data_[1]['variable'] = $initialInstallSN['jym'];
                $data_[1]['weizhi'] = 7;
                $this->fasong_s($data_, 2, 1);
            }

            // Log::info('加药时间：' . $jyf . ':' . $jym);
            if ($bxz == 1 && (($jsf != $initialInstallSN['jsf']) || ($jsm != $initialInstallSN['jsm']))) {
                $data = [];
                $data[0]['clientid'] = $devicename;
                $data[0]['variable'] = $initialInstallSN['jsf'];
                $data[0]['weizhi'] = 4;
                $data[1]['clientid'] = $devicename;
                $data[1]['variable'] = $initialInstallSN['jsm'];
                $data[1]['weizhi'] = 5;
                $this->fasong_s($data, 2, 1);
            } elseif ($bxz == 2 && (($jsf != $initialInstallSN['jsf2']) || ($jsm != $initialInstallSN['jsm2']))) {
                $data = [];
                $data[0]['clientid'] = $devicename;
                $data[0]['variable'] = $initialInstallSN['jsf2'];
                $data[0]['weizhi'] = 4;
                $data[1]['clientid'] = $devicename;
                $data[1]['variable'] = $initialInstallSN['jsm2'];
                $data[1]['weizhi'] = 5;
                $this->fasong_s($data, 2, 1);
            }
            // Log::info('加水时间：' . $jsf . ':' . $jsm);
        }


        //检查当时使用的泵跟对应泵的加水时间是否一致，不一致则下发加水时间
        if (!empty($generalSN) && !empty($initialInstallSN)) {
            //检验总牛数和设置的是否一样,不一致再次下发系统设置的牛数
            if ($zns != $generalSN['zns']) {
                $data = [];
                // $data[0]['clientid'] = $devicename;
                // $data[0]['variable'] = $generalSN['zns'];
                // $data[0]['weizhi'] = 20;

                $data[0]['clientid'] = $devicename;
                $data[0]['variable'] = $generalSN['zns'];
                $data[0]['weizhi'] = 20;
                $data[1]['clientid'] = $devicename;
                $data[1]['variable'] = $generalSN['nswc'];
                $data[1]['weizhi'] = 22;
                $data[2]['clientid'] = $devicename;
                $data[2]['variable'] = $generalSN['hxns'];
                $data[2]['weizhi'] = 23;
                $data[3]['clientid'] = $devicename;
                $data[3]['variable'] = $generalSN['fan'];
                $data[3]['weizhi'] = 103;
                // $this->fasong($data);
            }
            //检验换液数和设置的是否一样,不一致再次下发系统设置的牛数
            // if ($hxns != $generalSN['hxns']) {
            //     $data = [];
            //     $data[0]['clientid'] = $devicename;
            //     $data[0]['variable'] = $generalSN['hxns'];
            //     $data[0]['weizhi'] = 23;
            //     $the = new MqttController;
            //     $the->fasong($data);
            // }
        }

        //常用设置
        if (empty($generalSN)) {
            //常用设置
            $add = [
                'sn' => $devicename,
                'zns' => $zns,
                'nswc' => $nswc,
                'hxns' => $hxns,
                // 'jyb' => $jyb,
                // 'jybs' => $jybs,
            ];
            DB::table('plc_general')->insertGetId($add);
        }
        //  else {
        //     $add = [
        //         'zns' => $zns,
        //         'nswc' => $nswc,
        //         'hxns' => $hxns,
        //         // 'jyb' => $jyb,
        //         // 'jybs' => $jybs,
        //     ];
        //     DB::table('plc_general')->where(['sn' => $devicename])->update($add);
        // }

        //报警设置
        if (empty($noticeSN)) {
            //报警设置
            $addS = [
                'sn' => $devicename,
                'weiyunxingsjsz' => $weiyunxingsjsz,
            ];
            DB::table('plc_error_notice')->insertGetId($addS);
        } else {
            //报警设置
            $addS = [
                'weiyunxingsjsz' => $weiyunxingsjsz,
            ];
            DB::table('plc_error_notice')->where(['sn' => $devicename])->update(['weiyunxingsjsz' => $weiyunxingsjsz]);
        }
        //初始设置
        if (empty($initialInstallSN)) {
            //初始设置
            $addSS = [
                'sn' => $devicename,
                'scf' => $scf,
                'scm' => $scm,
                'zcf' => $zcf,
                'zcm' => $zcm,
                'jsf' => $jsf,
                'jsm' => $jsm,
                'jsf2' => $jsf,
                'jsm2' => $jsm,
                'jyf' => $jyf,
                'jym' => $jym,
                'qxs' => $qxs,
                'qxf' => $qxf,
                'cxyskq' => $cxm,
                'psysgb' => $psm,
                'gys' => $gys,
                'gyf' => $gyf,
                'gym' => $gym,
                'cxqgk' => $cxkm,
                'cxqgg' => $cxgm,
            ];

            DB::table('plc_initial_install')->insertGetId($addSS);
        } else {
            //初始设置
            $addSS = [
                // 'sn' => $devicename,
                'scf' => $scf,
                'scm' => $scm,
                'zcf' => $zcf,
                'zcm' => $zcm,
                // 'jsf' => $jsf,
                // 'jsm' => $jsm,
                // 'jyf' => $jyf,
                // 'jym' => $jym,
                'qxs' => $qxs,
                'qxf' => $qxf,
                'cxyskq' => $cxm,
                'psysgb' => $psm,
                'gys' => $gys,
                'gyf' => $gyf,
                'gym' => $gym,
                'cxqgk' => $cxkm,
                'cxqgg' => $cxgm
            ];
            // writeFile($addSS,'mqtt333.txt');
            DB::table('plc_initial_install')->where(['sn' => $devicename])->update($addSS);
        }

        //泵余量
        if (empty($detailsSN)) {
            $addSSS = [
                'sn' => $devicename,
                'bstate' => $bstate,
                'bstates' => $bstates,
                'zcq' => $zcq,
                'ms' => $ms,
                'bxz' => $bxz,
                'jrcq' => $jrcq,
                'bcyjc' => $bcyjc,
                'functionnumber' => $functionnumber,
                'pump1_action' => $pump1_action,
                'pump2_action' => $pump2_action,
            ];
            DB::table('plc_device_details')->insertGetId($addSSS);
        } else {
            //泵余量
            $addSSS = [
                // 'breset' => $breset,
                // 'bresets' => $bresets,
                'bstate' => $bstate,
                'bstates' => $bstates,
                'zcq' => $zcq,
                'ms' => $ms,
                'bxz' => $bxz,
                'jrcq' => $jrcq,
                'bcyjc' => $bcyjc,
                // 'pumpll' => $pumpll,
                // 'pumplls' => $pumplls,
                'functionnumber' => $functionnumber,
                'pump1_action' => $pump1_action,
                'pump2_action' => $pump2_action,
            ];
            //泵运行次数跟上次比是否有变化，运行几次，泵余量则减去对应的数
            //总容量
            $total = DB::table('plc_initial_install')->where('sn', $devicename)->orderBy('id', 'desc')->limit(1)->value('rl');
            $info = DB::table('plc_general')->where(['sn' => $devicename])->first();
            if ($pump1_action > $detailsSN['pump1_action']) {
                $bsurplus = (int) ($detailsSN['bsurplus'] - $total * ($info->jyb / 100) * ($pump1_action - $detailsSN['pump1_action']));
                $bsurplus = $bsurplus < 0 ? 0 : $bsurplus;
                $addSSS['bsurplus'] = $bsurplus;
            }
            if ($pump2_action > $detailsSN['pump2_action']) {
                $bsurpluss = (int) ($detailsSN['bsurpluss'] - $total * ($info->jybs / 100) * ($pump2_action - $detailsSN['pump2_action']));
                $bsurpluss = $bsurpluss < 0 ? 0 : $bsurpluss;
                $addSSS['bsurpluss'] = $bsurpluss;
            }
            DB::table('plc_device_details')->where(['sn' => $devicename])->update($addSSS);
        }

        //使用时间设置(新)
        if (empty($dateSN)) {
            $addSSSS = [
                'sn' => $devicename,
                'bone' => $bone,
                'btwo' => $btwo,
                'bthree' => $bthree,
                'bfour' => $bfour,
                'bfive' => $bfive,
                'bsix' => $bsix,
                'bseven' => $bseven,
                'bones' => $bones,
                'btwos' => $btwos,
                'bthrees' => $bthrees,
                'bfours' => $bfours,
                'bfives' => $bfives,
                'bsixs' => $bsixs,
                'bsevens' => $bsevens,
            ];
            DB::table('plc_date')->insertGetId($addSSSS);
        } else {
            $addSSSS = [
                'sn' => $devicename,
                'bone' => $bone,
                'btwo' => $btwo,
                'bthree' => $bthree,
                'bfour' => $bfour,
                'bfive' => $bfive,
                'bsix' => $bsix,
                'bseven' => $bseven,
                'bones' => $bones,
                'btwos' => $btwos,
                'bthrees' => $bthrees,
                'bfours' => $bfours,
                'bfives' => $bfives,
                'bsixs' => $bsixs,
                'bsevens' => $bsevens,
            ];
            DB::table('plc_date')->where(['sn' => $devicename])->update($addSSSS);
        }

        //定时启动设置
        if (empty($timingSN)) {
            //定时启动设置
            $addSSSSS = [
                'sn' => $devicename,
                'qdsA' => $qdsA,
                'qdsB' => $qdsB,
                'qdsC' => $qdsC,
                'qdsD' => $qdsD,
                'qdsE' => $qdsE,
                'qdsF' => $qdsF,
                'qdsG' => $qdsG,
                'qdsH' => $qdsH,
                'qdsI' => $qdsI,
                'qdsJ' => $qdsJ,
                'qdsK' => $qdsK,
                'qdsL' => $qdsL,
                'qdsM' => $qdsM,
                'qdsN' => $qdsN,
                'qdsO' => $qdsO,
                'qdsP' => $qdsP,
                'qdsQ' => $qdsQ,
                'qdsR' => $qdsR,
                'qdsS' => $qdsS,
                'qdsT' => $qdsT,
                'qdfA' => $qdfA,
                'qdfB' => $qdfB,
                'qdfC' => $qdfC,
                'qdfD' => $qdfD,
                'qdfE' => $qdfE,
                'qdfF' => $qdfF,
                'qdfG' => $qdfG,
                'qdfH' => $qdfH,
                'qdfI' => $qdfI,
                'qdfJ' => $qdfJ,
                'qdfK' => $qdfK,
                'qdfL' => $qdfL,
                'qdfM' => $qdfM,
                'qdfN' => $qdfN,
                'qdfO' => $qdfO,
                'qdfP' => $qdfP,
                'qdfQ' => $qdfQ,
                'qdfR' => $qdfR,
                'qdfS' => $qdfS,
                'qdfT' => $qdfT,
            ];

            DB::table('plc_timing')->insertGetId($addSSSSS);
        } else {
            //定时启动设置
            $addSSSSS = [
                'qdsA' => $qdsA,
                'qdsB' => $qdsB,
                'qdsC' => $qdsC,
                'qdsD' => $qdsD,
                'qdsE' => $qdsE,
                'qdsF' => $qdsF,
                'qdsG' => $qdsG,
                'qdsH' => $qdsH,
                'qdsI' => $qdsI,
                'qdsJ' => $qdsJ,
                'qdsK' => $qdsK,
                'qdsL' => $qdsL,
                'qdsM' => $qdsM,
                'qdsN' => $qdsN,
                'qdsO' => $qdsO,
                'qdsP' => $qdsP,
                'qdsQ' => $qdsQ,
                'qdsR' => $qdsR,
                'qdsS' => $qdsS,
                'qdsT' => $qdsT,
                'qdfA' => $qdfA,
                'qdfB' => $qdfB,
                'qdfC' => $qdfC,
                'qdfD' => $qdfD,
                'qdfE' => $qdfE,
                'qdfF' => $qdfF,
                'qdfG' => $qdfG,
                'qdfH' => $qdfH,
                'qdfI' => $qdfI,
                'qdfJ' => $qdfJ,
                'qdfK' => $qdfK,
                'qdfL' => $qdfL,
                'qdfM' => $qdfM,
                'qdfN' => $qdfN,
                'qdfO' => $qdfO,
                'qdfP' => $qdfP,
                'qdfQ' => $qdfQ,
                'qdfR' => $qdfR,
                'qdfS' => $qdfS,
                'qdfT' => $qdfT,
            ];
            DB::table('plc_timing')->where(['sn' => $devicename])->update($addSSSSS);
        }

        //plc时间与系统时间对比，不同则更新plc时间
        $plc_time = strtotime($dn . '-' . $dy . '-' . $dr . ' ' . $ds . ':' . $df . ':' . $dm);
        $w = date('w') + 1;
        $data = [];
        // Log::info('tt_' . $dn . '-' . $dy . '-' . $dr . ' ' . $ds . ':' . $df . ':' . $dm . '---------------------' . date('Y-m-d H:i:s'));
        if (abs(time() - $plc_time) > 10 || $w != $dxq) { //允许10秒误差

            $i = 0;
            $time_data = [date("y"), date("m"), date("d"), date("H"), date("i"), date("s"), date('w') + 1, 1];
            // $time_data = [9];
            $location_data = [24, 25, 26, 27, 28, 29, 30, 31];
            // $location_data = [48, 50, 52, 54, 56, 58, 60, 62];
            while ($i < 8) {
                $data[$i]['clientid'] = $devicename;
                $data[$i]['variable'] = $time_data[$i];
                $data[$i]['weizhi'] = $location_data[$i];
                $i = $i + 1;
            }
            return $this->fasong_s($data, 8);
        }

        //判断是否开启报警，发送报警通知,根据后台设置的通知方式发送短信或邮件
        if (!empty($noticeSN)) {
            $the = new CodeController;
            // 获取用户信息（手机号、邮箱和语言设置）
            $userInfo = DB::table('plc_user as t1')
                ->leftJoin('plc_device_user as t2', 't1.id', 't2.qiye_id')
                ->where('t1.del', null)
                ->where('t2.del', null)
                ->where('t2.device_number', $devicename)
                ->select('t1.phone', 't1.email', 't1.language')
                ->first();

            $phone = $userInfo->phone ?? '';
            $email = $userInfo->email ?? '';
            $language = $userInfo->language ?? 'zh_CN';

            if ($noticeSN['slgyq'] == 1) {
                $hys = DB::table('plc_general')->where('sn', $devicename)->value('hxns');
                if ($bcyjc > $hys) {
                    $alertMessage = AlertMessageService::getAlertMessage('detection_count_exceeded', $language, [
                        'count' => $bcyjc,
                        'limit' => $hys
                    ]);
                    $notificationData = [
                        'phone' => $phone,
                        'email' => $email,
                        'devicename' => $devicename,
                        'contents' => $alertMessage
                    ];
                    $the->sendSmartNotification($notificationData);
                }
            }
            if ($noticeSN['waterPump'] == 1) {
                if ($waterPump == 2) { //水泵故障
                    $alertMessage = AlertMessageService::getAlertMessage('water_pump_failure', $language);
                    $notificationData = [
                        'phone' => $phone,
                        'email' => $email,
                        'devicename' => $devicename,
                        'contents' => $alertMessage
                    ];
                    $the->sendSmartNotification($notificationData);
                }
            }
        }
    }
    // $data_[0]['clientid'] = $devicename;
    //                 $data_[0]['variable'] = $min;
    //                 $data_[0]['weizhi'] = 6;
    //                 $data_[1]['clientid'] = $devicename;
    //                 $data_[1]['variable'] = $sec;
    //                 $data_[1]['weizhi'] = 7;
    public function fasong_s($data, $len, $from = 0)
    {
        $clientid = $data[0]['clientid'];
        $weizhi = $data[0]['weizhi'];
        $bytearry = [];
        $bytearry[0] = 1;
        $bytearry[1] = 16;
        $bytearry[2] = $weizhi >> 8 & 0xff;
        $bytearry[3] = $weizhi & 0xff;
        $bytearry[4] = 0;
        $bytearry[5] = $len;
        $bytearry[6] = $len * 2;
        $string = sprintf("%02x%02x%02x%02x%02x%02x%02x", $bytearry[0], $bytearry[1], $bytearry[2], $bytearry[3], $bytearry[4], $bytearry[5], $bytearry[6]);
        $i = 0;
        foreach ($data as $k => $v) {

            $variable = $v['variable'];

            $bytearry[7 + $k * 2] = $variable >> 8 & 0xff;
            $bytearry[7 + $k * 2 + 1] = $variable & 0xff;
            $string .= sprintf("%02X%02X", $bytearry[7 + $k * 2], $bytearry[7 + $k * 2 + 1]);
            $i = ($k + 1) * 2;
        }

        $datas = $this->crc($string);
        $bytearry[7 + $i] = $datas['low8'];
        $bytearry[8 + $i] = $datas['high8'];
        $outstr = $string . $bytearry[7 + $i] . $bytearry[8 + $i];
        $payload = base64_encode(pack("H*", $outstr));
        $payloads['clientid'] = $clientid;
        $payloads['payload'] = $payload;
        if($from == 0) {
            $this->fasongs($payloads);
        }else{
            $this->fasongss($payloads);
        }
        // Log::info(json_encode($payloads));
        return json_encode(['resutlt' => 0, 'message' => '成功']);
    }

    //重启接口
    public function restart(Request $request)
    {
        $info = $request->all();
        $token = $request->header('token');
        $uid = $this->getPe($token);
        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        $clientid = $info['sn'];
        $data[0]['clientid'] = $clientid;
        $data[0]['variable'] = 1;
        $data[0]['weizhi'] = 106;
        $the = new MqttController;
        $the->fasong($data);
        return json_encode(['resutlt' => 0, 'message' => '成功']);
    }

    public function fasong($data)
    {
        $clientid = $data[0]['clientid'];
        foreach ($data as $k => $v) {
            $weizhi = $v['weizhi'];
            $variable = $v['variable'];
            $bytearry = [];
            $bytearry[0] = 1;
            $bytearry[1] = 6;
            $bytearry[2] = $weizhi >> 8 & 0xff;
            $bytearry[3] = $weizhi & 0xff;
            $bytearry[4] = $variable >> 8 & 0xff;
            $bytearry[5] = $variable & 0xff;
            $string = sprintf("%02x%02x%02x%02x%02x%02x", $bytearry[0], $bytearry[1], $bytearry[2], $bytearry[3], $bytearry[4], $bytearry[5]);
            $datas = $this->crc($string);
            $bytearry[6] = $datas['low8'];
            $bytearry[7] = $datas['high8'];
            $outstr = sprintf("%02x%02x%02x%02x%02x%02x%s%s", $bytearry[0], $bytearry[1], $bytearry[2], $bytearry[3], $bytearry[4], $bytearry[5], $bytearry[6], $bytearry[7]);
            $payload[$k] = base64_encode(pack("H*", $outstr));
            $sss[$k] = $outstr;
        }

        for ($x = 0; $x < count($payload); $x++) {
            $payloads['clientid'] = $clientid;
            $payloads['payload'] = $payload[$x];
            $res[$x] = $this->fasongs($payloads);
            sleep(1);
        }
        return json_encode(['resutlt' => 0, 'message' => '成功']);
    }

    public function fasongss($payloads)
    {

        $client = new \GuzzleHttp\Client(['base_uri' => 'http://127.0.0.1:18083/api/v5/publish']);
        $res = $client->post(
            '',
            [
                'headers' => [
                    'Content-type' => 'application/json',
                    'Authorization' => 'Basic ZDVjZGE3Nzc4Zjg4MmUyZDpTYXAzOUFuUElROUNJVFlEdzlCajBmaFc1eGJwWFk3V0FtSU05QzJkYWk2VDRSTQ==',
                    'Accept' => 'application/json',
                ],
                'json' => [
                    'topic' => $payloads['clientid'],
                    'payload' => $payloads['payload'],
                    'payload_encoding' => 'base64',
                    'qos' => 0,
                    'retain' => false,
                    'clientid' => 'tt_http',
                ],
            ]
        );
        sleep(10);
        return 123;
    }


    public function fasongs($payloads)
    {

        $client = new \GuzzleHttp\Client(['base_uri' => 'http://127.0.0.1:18083/api/v5/publish']);
        $res = $client->post(
            '',
            [
                'headers' => [
                    'Content-type' => 'application/json',
                    'Authorization' => 'Basic ZDVjZGE3Nzc4Zjg4MmUyZDpTYXAzOUFuUElROUNJVFlEdzlCajBmaFc1eGJwWFk3V0FtSU05QzJkYWk2VDRSTQ==',
                    'Accept' => 'application/json',
                ],
                'json' => [
                    'topic' => $payloads['clientid'],
                    'payload' => $payloads['payload'],
                    'payload_encoding' => 'base64',
                    'qos' => 0,
                    'retain' => false,
                    'clientid' => 'tt_http',
                ],
            ]
        );
        return 123;
    }

    //每天置0当天的重启次数
    public function Set()
    {
        // date_default_timezone_set('PRC'); //设置中国时区
        // $starttime = strtotime(date('Y-m-d') . '00:00:00');
        // $endtime = $starttime + 180;
        // $time = time();
        // if ($starttime <= $time && $time <= $endtime) {
            DB::table('plc_device_details')->where('id', '>', '0')->update(['jrcq' => 0]);
        // }
    }

    /**
     * crc16计算
     * 传入字符串格式：001624180101
     * 返回值格式：[高8位,低8位]
     */
    public function crc($string)
    {
        // dd($string);
        $string = pack('H*', $string);
        // dd($string);
        $crc = 0xFFFF;
        for ($x = 0; $x < strlen($string); $x++) {
            $crc = $crc ^ ord($string[$x]);
            for ($y = 0; $y < 8; $y++) {
                if (($crc & 0x0001) == 0x0001) {
                    $crc = (($crc >> 1) ^ 0xA001);
                } else {
                    $crc = $crc >> 1;
                }
            }
        }
        // dd($crc);
        $high8 = str_pad(dechex(floor($crc / 256)), 2, '0', STR_PAD_LEFT);
        $low8 = str_pad(dechex($crc % 256), 2, '0', STR_PAD_LEFT);
        $data['low8'] = $low8;
        $data['high8'] = $high8;
        return $data;
    }
}
