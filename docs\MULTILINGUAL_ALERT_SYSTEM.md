# 多语言报警消息系统

## 功能概述

本系统实现了基于用户语言设置的多语言报警消息功能，支持：
- **简体中文 (zh_CN)**
- **英语 (en)**
- **繁体中文 (zh_TW)**

系统会根据 `plc_user` 表中的 `language` 字段自动选择对应语言的报警消息。

## 文件结构

### 1. 配置文件
- `config/alert_messages.php` - 多语言报警消息配置

### 2. 服务类
- `app/Services/AlertMessageService.php` - 多语言消息服务

### 3. 测试控制器
- `app/Http/Controllers/Api/AlertMessageTestController.php` - 测试功能

### 4. 已修改的控制器
- `app/Http/Controllers/Api/ErrorController.php` - 设备错误监测
- `app/Http/Controllers/Api/MqttController.php` - MQTT消息处理

## 支持的报警消息类型

| 消息键名 | 中文简体 | 英语 | 繁体中文 |
|---------|---------|------|---------|
| `long_time_not_running` | 长时间未运行 | Long time not running | 長時間未運行 |
| `pump1_level_alarm` | 泵1液位报警 | Pump 1 level alarm | 泵1液位報警 |
| `pump2_level_alarm` | 泵2液位报警 | Pump 2 level alarm | 泵2液位報警 |
| `water_pump_failure` | 水泵故障 | Water pump failure | 水泵故障 |
| `detection_count_exceeded` | 本次已检测数量({count})大于换液数({limit}) | Detection count ({count}) exceeds liquid change limit ({limit}) | 本次已檢測數量({count})大於換液數({limit}) |
| `device_offline` | 设备离线 | Device offline | 設備離線 |
| `temperature_abnormal` | 温度异常 | Temperature abnormal | 溫度異常 |
| `pressure_abnormal` | 压力异常 | Pressure abnormal | 壓力異常 |
| `flow_abnormal` | 流量异常 | Flow abnormal | 流量異常 |
| `voltage_abnormal` | 电压异常 | Voltage abnormal | 電壓異常 |
| `sensor_failure` | 传感器故障 | Sensor failure | 傳感器故障 |
| `communication_error` | 通信异常 | Communication error | 通信異常 |
| `system_error` | 系统错误 | System error | 系統錯誤 |

## 使用方法

### 1. 基本用法

```php
use App\Services\AlertMessageService;

// 获取指定语言的报警消息
$message = AlertMessageService::getAlertMessage('long_time_not_running', 'en');
// 结果: "Long time not running"

// 带参数的消息
$message = AlertMessageService::getAlertMessage('detection_count_exceeded', 'zh_CN', [
    'count' => 150,
    'limit' => 100
]);
// 结果: "本次已检测数量(150)大于换液数(100)"
```

### 2. 根据用户ID获取语言设置

```php
$language = AlertMessageService::getUserLanguage($userId);
$message = AlertMessageService::getAlertMessage('pump1_level_alarm', $language);
```

### 3. 根据设备号获取用户语言设置

```php
$language = AlertMessageService::getUserLanguageByDevice($deviceNumber);
$message = AlertMessageService::getAlertMessage('water_pump_failure', $language);
```

### 4. 在控制器中的使用示例

```php
// ErrorController 中的使用
$userInfo = DB::table('plc_user')->where('id', $userId)->select('phone', 'email', 'language')->first();
$language = $userInfo->language ?? 'zh_CN';

$alertMessage = AlertMessageService::getAlertMessage('long_time_not_running', $language);
$notificationData = [
    'phone' => $phone,
    'email' => $email,
    'devicename' => $devicename,
    'contents' => $alertMessage
];
$the->sendSmartNotification($notificationData);
```

## 测试接口

### 1. 测试多语言消息
```
GET /api/test/alert-messages?language=en
```

### 2. 测试用户语言设置
```
GET /api/test/user-language?user_id=1
```

### 3. 测试设备用户语言设置
```
GET /api/test/device-user-language?device_number=DEVICE001
```

### 4. 获取支持的语言列表
```
GET /api/test/supported-languages
```

### 5. 批量获取消息
```
GET /api/test/batch-messages?language=zh_TW
```

## 配置说明

### 添加新的报警消息

在 `config/alert_messages.php` 中添加新的消息：

```php
'new_alert_type' => [
    'zh_CN' => '新的报警类型',
    'en' => 'New alert type',
    'zh_TW' => '新的報警類型'
],
```

### 添加新的语言支持

1. 在配置文件中添加新语言到 `supported_languages` 数组
2. 为每个消息添加新语言的翻译
3. 在 `AlertMessageService` 中更新 `getLanguageDisplayName` 方法

## 数据库要求

确保 `plc_user` 表包含 `language` 字段：

```sql
ALTER TABLE `plc_user` ADD COLUMN `language` VARCHAR(10) DEFAULT 'zh_CN' COMMENT '用户语言设置' AFTER `email`;
```

支持的语言值：
- `zh_CN` - 简体中文
- `en` - 英语  
- `zh_TW` - 繁体中文

## 错误处理

- 如果指定的语言不存在，系统会回退到默认语言（zh_CN）
- 如果消息键名不存在，返回键名本身
- 如果用户语言设置为空，使用默认语言
- 数据库查询失败时，使用默认语言

## 扩展功能

### 1. 动态参数替换

消息中可以使用 `{参数名}` 格式的占位符：

```php
$message = AlertMessageService::getAlertMessage('detection_count_exceeded', 'en', [
    'count' => 200,
    'limit' => 150
]);
```

### 2. 批量获取消息

```php
$messages = AlertMessageService::getBatchMessages([
    'long_time_not_running',
    'pump1_level_alarm',
    'pump2_level_alarm'
], 'en');
```

## 快速测试

### 使用浏览器测试

1. 访问测试接口查看所有支持的语言：
   ```
   http://your-domain/api/test/supported-languages
   ```

2. 测试中文消息：
   ```
   http://your-domain/api/test/alert-messages?language=zh_CN
   ```

3. 测试英文消息：
   ```
   http://your-domain/api/test/alert-messages?language=en
   ```

4. 测试繁体中文消息：
   ```
   http://your-domain/api/test/alert-messages?language=zh_TW
   ```

### 使用命令行测试

```bash
# 测试多语言消息
curl "http://your-domain/api/test/alert-messages?language=en"

# 测试用户语言设置
curl "http://your-domain/api/test/user-language?user_id=1"

# 测试设备用户语言设置
curl "http://your-domain/api/test/device-user-language?device_number=DEVICE001"
```

## 注意事项

1. **性能考虑**：消息配置会被缓存，修改配置后需要清除缓存
2. **一致性**：确保所有支持的语言都有对应的翻译
3. **参数验证**：使用参数替换时，确保提供所有必需的参数
4. **向后兼容**：添加新消息时，保持现有消息键名不变
5. **数据库字段**：确保 `plc_user` 表的 `language` 字段已正确设置
