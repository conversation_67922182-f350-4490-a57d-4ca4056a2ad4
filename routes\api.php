<?php

use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});

// 多语言报警消息测试路由
Route::prefix('test')->group(function () {
    Route::get('alert-messages', 'Api\AlertMessageTestController@testAlertMessages');
    Route::get('user-language', 'Api\AlertMessageTestController@testUserLanguage');
    Route::get('device-user-language', 'Api\AlertMessageTestController@testDeviceUserLanguage');
    Route::get('supported-languages', 'Api\AlertMessageTestController@getSupportedLanguages');
    Route::get('batch-messages', 'Api\AlertMessageTestController@getBatchMessages');
});
