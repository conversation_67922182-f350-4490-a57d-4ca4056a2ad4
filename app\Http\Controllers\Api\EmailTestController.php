<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\EmailNotificationService;
use App\Services\IpLocationService;
use App\Http\Controllers\Home\CodeController;
use Illuminate\Support\Facades\Log;

class EmailTestController extends Controller
{
    protected $emailService;
    protected $ipLocationService;

    public function __construct()
    {
        $this->emailService = new EmailNotificationService();
        $this->ipLocationService = new IpLocationService();
    }

    /**
     * 测试邮件发送功能
     */
    public function testEmail(Request $request)
    {
        $email = $request->input('email');
        if (!$email) {
            return response()->json([
                'success' => false,
                'message' => '请提供邮箱地址'
            ]);
        }

        if (!$this->emailService->isValidEmail($email)) {
            return response()->json([
                'success' => false,
                'message' => '邮箱格式不正确'
            ]);
        }

        $success = $this->emailService->sendTestEmail($email);

        return response()->json([
            'success' => $success,
            'message' => $success ? '测试邮件发送成功' : '测试邮件发送失败'
        ]);
    }

    /**
     * 测试设备报警邮件
     */
    public function testDeviceAlert(Request $request)
    {
        $email = $request->input('email','<EMAIL>');
        $deviceName = $request->input('device_name', 'TEST_DEVICE_001');
        $alertContent = $request->input('alert_content', '这是一个测试报警信息');

        if (!$email) {
            return response()->json([
                'success' => false,
                'message' => '请提供邮箱地址'
            ]);
        }

        $success = $this->emailService->sendDeviceAlert($email, $deviceName, $alertContent);

        return response()->json([
            'success' => $success,
            'message' => $success ? '设备报警邮件发送成功' : '设备报警邮件发送失败'
        ]);
    }

    /**
     * 测试IP地理位置检测
     */
    public function testIpLocation(Request $request)
    {
        $clientIp = $this->ipLocationService->getClientIp($request);
        $isMainlandChina = $this->ipLocationService->isMainlandChina($request,$clientIp);
        $locationInfo = $this->ipLocationService->getLocationInfo($request, $clientIp);

        return [
            'ip' => $clientIp,
            'is_mainland_china' => $isMainlandChina,
            'location_info' => $locationInfo
        ];
    }

    /**
     * 测试智能通知功能
     */
    public function testSmartNotification(Request $request)
    {
        $phone = $request->input('phone', '13800138000');
        $email = $request->input('email');
        $deviceName = $request->input('device_name', 'TEST_DEVICE_001');
        $alertContent = $request->input('alert_content', '这是一个测试报警信息');
        $testIp = $request->input('test_ip'); // 可以指定测试IP

        if (!$email) {
            return response()->json([
                'success' => false,
                'message' => '请提供邮箱地址进行测试'
            ]);
        }

        $notificationData = [
            'phone' => $phone,
            'email' => $email,
            'devicename' => $deviceName,
            'contents' => $alertContent
        ];

        $codeController = new CodeController();
        
        try {
            $result = $codeController->sendSmartNotification($notificationData, $testIp);
            
            return response()->json([
                'success' => true,
                'message' => '智能通知测试完成',
                'result' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('智能通知测试失败: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => '智能通知测试失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取邮件配置信息
     */
    public function getMailConfig()
    {
        return response()->json([
            'success' => true,
            'config' => [
                'driver' => config('mail.driver'),
                'host' => config('mail.host'),
                'port' => config('mail.port'),
                'encryption' => config('mail.encryption'),
                'from_address' => config('mail.from.address'),
                'from_name' => config('mail.from.name'),
            ]
        ]);
    }
}
