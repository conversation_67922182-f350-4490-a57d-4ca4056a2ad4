<?php

namespace App\Services;

class AlertMessageService
{
    /**
     * 获取报警消息
     * @param string $messageKey 消息键名
     * @param string $language 语言代码 (zh_CN, en, zh_TW)
     * @param array $params 消息参数（用于替换占位符）
     * @return string
     */
    public static function getAlertMessage($messageKey, $language = null, $params = [])
    {
        // 获取配置
        $config = config('alert_messages');
        $messages = $config['alert_messages'] ?? [];
        $defaultLanguage = $config['default_language'] ?? 'zh_CN';
        $supportedLanguages = $config['supported_languages'] ?? ['zh_CN', 'en', 'zh_TW'];
        
        // 验证语言代码
        if (!$language || !in_array($language, $supportedLanguages)) {
            $language = $defaultLanguage;
        }
        
        // 获取消息
        if (!isset($messages[$messageKey])) {
            return $messageKey; // 如果找不到消息键，返回键名本身
        }
        
        $messageGroup = $messages[$messageKey];
        
        // 获取指定语言的消息，如果不存在则使用默认语言
        $message = $messageGroup[$language] ?? $messageGroup[$defaultLanguage] ?? $messageKey;
        
        // 替换参数占位符
        if (!empty($params)) {
            foreach ($params as $key => $value) {
                $message = str_replace('{' . $key . '}', $value, $message);
            }
        }
        
        return $message;
    }
    
    /**
     * 根据用户ID获取用户语言设置
     * @param int $userId 用户ID
     * @return string
     */
    public static function getUserLanguage($userId)
    {
        try {
            $user = \DB::table('plc_user')->where('id', $userId)->where('del', null)->first();
            return $user->language ?? config('alert_messages.default_language', 'zh_CN');
        } catch (\Exception $e) {
            return config('alert_messages.default_language', 'zh_CN');
        }
    }
    
    /**
     * 根据设备号获取用户语言设置
     * @param string $deviceNumber 设备号
     * @return string
     */
    public static function getUserLanguageByDevice($deviceNumber)
    {
        try {
            $user = \DB::table('plc_user as t1')
                ->leftJoin('plc_device_user as t2', 't1.id', '=', 't2.qiye_id')
                ->where('t2.device_number', $deviceNumber)
                ->where('t1.del', null)
                ->where('t2.del', null)
                ->select('t1.language')
                ->first();
            
            return $user->language ?? config('alert_messages.default_language', 'zh_CN');
        } catch (\Exception $e) {
            return config('alert_messages.default_language', 'zh_CN');
        }
    }
    
    /**
     * 获取所有支持的语言
     * @return array
     */
    public static function getSupportedLanguages()
    {
        return config('alert_messages.supported_languages', ['zh_CN', 'en', 'zh_TW']);
    }
    
    /**
     * 获取语言显示名称
     * @param string $languageCode
     * @return string
     */
    public static function getLanguageDisplayName($languageCode)
    {
        $displayNames = [
            'zh_CN' => '简体中文',
            'en' => 'English',
            'zh_TW' => '繁體中文'
        ];
        
        return $displayNames[$languageCode] ?? $languageCode;
    }
    
    /**
     * 批量获取多语言消息
     * @param array $messageKeys 消息键名数组
     * @param string $language 语言代码
     * @return array
     */
    public static function getBatchMessages($messageKeys, $language = null)
    {
        $result = [];
        foreach ($messageKeys as $key) {
            $result[$key] = self::getAlertMessage($key, $language);
        }
        return $result;
    }
}
