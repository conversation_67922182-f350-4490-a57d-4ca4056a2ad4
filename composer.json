{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": ">=7.0.0", "bluerhinos/phpmqtt": "^1.0", "fideloper/proxy": "~3.3", "guzzlehttp/guzzle": "~6.0", "laravel/framework": "5.5.*", "laravel/tinker": "~1.0", "predis/predis": "1.1", "qcloudsms/qcloudsms_php": "0.1.*", "tencentcloud/tencentcloud-sdk-php": "^3.0", "tymon/jwt-auth": "^1.0"}, "require-dev": {"filp/whoops": "~2.0", "fzaninotto/faker": "~1.4", "mockery/mockery": "~1.0", "phpunit/phpunit": "~6.0", "symfony/thanks": "^1.0"}, "autoload": {"classmap": ["database/seeds", "database/factories"], "psr-4": {"App\\": "app/"}, "files": ["app/functions/functions.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "extra": {"laravel": {"dont-discover": []}}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "allow-plugins": {"kylekatarnls/update-helper": true, "symfony/thanks": true}}}