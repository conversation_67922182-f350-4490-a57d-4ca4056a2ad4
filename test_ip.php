<?php
/**
 * 简单的IP获取测试脚本
 * 用于验证IP获取逻辑是否正确
 */

echo "=== IP获取测试 ===\n\n";

// 显示所有相关的服务器变量
$ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];

echo "服务器变量:\n";
foreach ($ipKeys as $key) {
    $value = $_SERVER[$key] ?? '未设置';
    echo "$key: $value\n";
}

echo "\n";

// 模拟Laravel Request::ip()的逻辑
function getClientIp() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) && !empty($_SERVER[$key])) {
            $ip = $_SERVER[$key];
            
            // 处理多个IP的情况
            if (strpos($ip, ',') !== false) {
                $ip = trim(explode(',', $ip)[0]);
            }
            
            // 验证IP格式（排除内网IP，获取公网IP）
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }

    // 如果没有找到公网IP，再检查是否有内网IP
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) && !empty($_SERVER[$key])) {
            $ip = $_SERVER[$key];
            
            if (strpos($ip, ',') !== false) {
                $ip = trim(explode(',', $ip)[0]);
            }
            
            // 验证IP格式（包括内网IP）
            if (filter_var($ip, FILTER_VALIDATE_IP)) {
                return $ip;
            }
        }
    }

    return '127.0.0.1';
}

$clientIp = getClientIp();
echo "检测到的客户端IP: $clientIp\n";

// 判断IP类型
if (filter_var($clientIp, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
    echo "IP类型: 公网IP\n";
} else {
    echo "IP类型: 内网IP或本地IP\n";
}

// 测试地理位置检测
echo "\n=== 地理位置检测测试 ===\n";
if ($clientIp !== '127.0.0.1') {
    $url = "http://ip-api.com/json/$clientIp?fields=status,country,countryCode,regionName,city";
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 5
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response) {
        $data = json_decode($response, true);
        if ($data && $data['status'] === 'success') {
            echo "国家: {$data['country']} ({$data['countryCode']})\n";
            echo "地区: {$data['regionName']}\n";
            echo "城市: {$data['city']}\n";
            echo "是否中国大陆: " . ($data['countryCode'] === 'CN' ? '是' : '否') . "\n";
            echo "建议通知方式: " . ($data['countryCode'] === 'CN' ? '短信' : '邮件') . "\n";
        } else {
            echo "地理位置检测失败\n";
        }
    } else {
        echo "无法连接到地理位置服务\n";
    }
} else {
    echo "本地IP，默认认为是中国大陆，使用短信通知\n";
}

echo "\n=== 测试完成 ===\n";
?>
