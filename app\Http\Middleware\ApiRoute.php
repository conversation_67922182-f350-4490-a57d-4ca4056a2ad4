<?php
namespace   App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
//use Illuminate\Support\Facades\Redis;

class ApiRoute
{
    public function handle($request, Closure $next)
    {
        $now_time = time();
        $date_now_time = date("Y-m-d H:i:s",$now_time);
        $user_id = 0;
        $role = 0;

//        $token = $request->all();
//        dd($token);
//        dd($id);
        if(!empty($request->input('token')))
        {
//
            //得到userid
            $token = substr(decrypts($request->input('token')),0,-10);
//            dd($token);
            if($token == 1)
            {
                $input = DB::table('plc_admin')->where('sort',$token)->select('id','role')->get()->toArray();
            }else{
                $input = DB::table('plc_user')->where('id',$token)->select('id','role')->get()->toArray();
            }
//            dd($input);
//            dd($input[0]['role']);
//            dd($token);
            $input = objToArr($input);
//            dd($input);
            $role = $input[0]['role'];
//            dd($role);
            $user_id = $input[0]['id'];
//            dd($user_id);
        }
//        $token =Crypt::decrypt($request->header('TOKEN'));
//        $id = $token;
//        dd($token);
        $request->merge(['sq_time'=>microtime(true)]);
        $response = $next($request);

        $rq_time = microtime(true)-$request->sq_time;
        //插入请求日志
        $request_url = $request->getRequestUri();
//        dd($request_url);
        DB::table('plc_request_log')
            ->insert([
                'user_id' =>$user_id,
                'role' =>$role,
                'header'=>json_encode($request->header()),
                'ip_address'=>$request->ip(),
                'method'=>$request->method(),
                'url' =>$request->fullUrl(),
                'param'=>json_encode($request->all()),
                'rq_time' =>sprintf("%.2f",$rq_time),
                'response'=>$response->getContent(),
                'created_at'=>$now_time,
                'updated_at'=>$now_time
        ]);
        return $response;
    }
}