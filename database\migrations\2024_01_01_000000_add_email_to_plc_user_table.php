<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddEmailToPlcUserTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('plc_user', function (Blueprint $table) {
            $table->string('email')->nullable()->after('phone')->comment('用户邮箱地址');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('plc_user', function (Blueprint $table) {
            $table->dropColumn('email');
        });
    }
}
