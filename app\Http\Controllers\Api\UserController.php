<?php

namespace App\Http\Controllers\Api;

use App\Model\UserModels;
use App\Rules\Mobile;
use Illuminate\Http\Request;
use App\Http\Controllers\Api\Controller;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Crypt;

class UserController extends Controller
{
    //渲染出所有企业信息
    public function list(Request $request)
    {
        $info = $request->all();
        date_default_timezone_set('PRC'); //设置中国时区
        $token = $request->header('token');
        $uid = $this->getPe($token);
        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        if ($uid['role'] == 0) {
            return json_encode(['resutlt' => 1, 'message' => '权限不足']);
        }
        $sousuo = $request->input('sousuo');
        $status = $request->input('status');

        if (!empty($info['pagesize'])) {
            $limit = $info['pagesize'];
        } else {
            $limit = 10;
        }

        if ($status == 1) {
            $user = UserModels::orderBy('id', 'asc')->where(['status' => 0])
                ->where(['del' => null])
                ->where(function ($query) use ($request) {
                    $where = $request->input('sousuo');
                    if (!empty($where)) {
                        $query->where('enterprise', 'like', '%' . $where . '%');
                        $query->orwhere('user_name', 'like', '%' . $where . '%');
                        $query->orwhere('user_names', 'like', '%' . $where . '%');
                        $query->orwhere('phone', 'like', '%' . $where . '%');
                        $query->orWhere('phones', 'like', '%' . $where . '%');
                    }
                })->paginate($limit);

            $user = objToArr($user);
            if (!$user) {
                return response()->json(['status' => 1, 'msg' => '未查询到相关信息！']);
            }
            foreach ($user['data'] as $k => $v) {
                $user['data'][$k]['created_at'] = date("Y-m-d H:i:s", $v['created_at']);
            }
            return response()->json(['status' => 0, 'msg' => '查询成功！', 'page' => $user['current_page'], 'last_page' => $user['last_page'], 'data' => $user]);
        } elseif ($status == 2) {
            $user = UserModels::orderBy('id', 'asc')->where(['status' => 1])
                ->where(['del' => null])
                ->where(function ($query) use ($request) {
                    $where = $request->input('sousuo');
                    if (!empty($where)) {
                        $query->where('enterprise', 'like', '%' . $where . '%');
                        $query->orwhere('user_name', 'like', '%' . $where . '%');
                        $query->orwhere('user_names', 'like', '%' . $where . '%');
                        $query->orwhere('phone', 'like', '%' . $where . '%');
                        $query->orWhere('phones', 'like', '%' . $where . '%');
                    }
                })->paginate($limit);

            $user = objToArr($user);
            if (!$user) {
                return response()->json(['status' => 1, 'msg' => '未查询到相关信息！']);
            }
            foreach ($user['data'] as $k => $v) {
                $user['data'][$k]['created_at'] = date("Y-m-d H:i:s", $v['created_at']);
            }
            return response()->json(['status' => 0, 'msg' => '查询成功！', 'page' => $user['current_page'], 'last_page' => $user['last_page'], 'data' => $user]);
        } elseif ($status == 0) {
            if ($sousuo == '') {
                $user = UserModels::orderBy('id', 'asc')->where(['del' => null])->paginate($limit);
            } else {
                $user = UserModels::orderBy('id', 'asc')
                    ->where(['del' => null])
                    ->where(function ($query) use ($request) {
                        $where = $request->input('sousuo');
                        if (!empty($where)) {
                            $query->where('enterprise', 'like', '%' . $where . '%');
                            $query->orwhere('user_name', 'like', '%' . $where . '%');
                            $query->orwhere('user_names', 'like', '%' . $where . '%');
                            $query->orwhere('phone', 'like', '%' . $where . '%');
                            $query->orWhere('phones', 'like', '%' . $where . '%');
                        }
                    })->paginate($limit);
            }
            $user = objToArr($user);
            if (!$user) {
                return response()->json(['status' => 1, 'msg' => '未查询到相关信息！']);
            }
            foreach ($user['data'] as $k => $v) {
                $user['data'][$k]['created_at'] = date("Y-m-d H:i:s", $v['created_at']);
            }
            return response()->json(['status' => 0, 'msg' => '查询成功！', 'page' => $user['current_page'], 'last_page' => $user['last_page'], 'data' => $user]);
        }

        $user = UserModels::orderBy('id', 'asc')->where(['del' => null])->paginate(5);
        foreach ($user as $k => $v) {
            $user[$k]['updated_at'] = date("Y-m-d H:i:s", $v['updated_at']);
        }
        return response()->json(['status' => 0, 'msg' => '查询成功！', 'page' => $user['current_page'], 'last_page' => $user['last_page'], 'data' => $user]);
    }

    //新增页面
    public function add()
    {
        //返回新增页面
    }

    //新增操作
    public function store(Request $request)
    {
        $token = $request->header('token');
        date_default_timezone_set('PRC'); //设置中国时区
        $uid = $this->getPe($token);
        if ($uid['role'] == 0) {
            return json_encode(['resutlt' => 1, 'message' => '权限不足']);
        }
        $user = $request->all();

        //进行验证
        $rule = [
            'enterprise' => 'required|unique:plc_user|between:4,18',
            'user_name'  => 'required|between:2,18',
            'phone'      => 'required|phone',
            'user_names' => 'required|between:2,18',
            'phones'     => 'required|phone',
            'account'    => 'required|unique:plc_user|between:4,18',
            'status'     => 'required',
            'language'   => 'required',
            'notification_method' => 'required',
            'email'      => 'required_if:notification_method,1|email'
        ];

        $msg = [
            'enterprise.required' => '企业名必须输入',
            'enterprise.between' => '企业名长度必需在4-18位之间',
            'user_name.required' => '联系人1必须输入',
            'phone.required'     => '手机号不能为空',
            'phone.phone'     => '手机号不合法',
            'user_name.between' => '联系人1长度必需在2-18位之间',
            'user_names.required' => '联系人2必须输入',
            'phones.required'     => '手机号不能为空',
            'phones.phone'     => '手机号不合法',
            'user_names.between' => '联系人2长度必需在2-18位之间',
            'account.required' => '账号必须输入',
            'account.between' => '账号长度必需在4-18位之间',
            'account.unique' => '账号重复',
            'enterprise.unique' => '企业名称重复',
            'status'        => '不存在status',
            'language.required' => '语言必须选择',
            'notification_method.required' => '通知方式必须选择',
            'email.required_if' => '当通知方式为邮件时，邮箱地址必须填写',
        ];
        $time = Carbon::now()->toDateTimeString();
        $enterprise_number = preg_replace('/[^\.0*********]/s', '', $time);
        $sort = preg_replace('/[^\.0*********]/s', '', $time);
        $validator = Validator::make($user, $rule, $msg);
        if ($validator->fails()) {
            return response()->json(['status' => 1, 'msg' => '添加失败！', 'data' => $validator->errors()]);
        }

        //添加到数据库
        if ($user['phone'] == $user['phones']) {
            return response()->json(['status' => 1, 'msg' => '联系人1与联系人2手机号不可以重复']);
        }
        $enterprise_number = 'U' . $enterprise_number;
        $arr = [
            'enterprise' => $user['enterprise'],
            'enterprise_number' => $enterprise_number,
            'user_name' => $user['user_name'],
            'phone'     => $user['phone'],
            'password' => Crypt::encrypt('*********'),
            'user_names' => $user['user_names'],
            'phones' => $user['phones'],
            'account' => $user['account'],
            'status'    => $user['status'],
            'content' => $user['content'],
            'sort' => $sort,
            'created_at' => time(),
            'updated_at' => time(),
            'language' => $user['language'],
            'notification_method' => $user['notification_method'],
            'email' => $user['email'] ?? null
        ];

        $res = UserModels::insertGetId($arr);
        if ($res) {
            return response()->json(['status' => 0, 'msg' => '添加成功！']);
        } else {
            return response()->json(['status' => 1, 'msg' => '添加失败！']);
        }
    }

    //编辑
    public function edit(Request $request)
    {
        $token = $request->header('token');
        $uid = $this->getPe($token);
        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        if ($uid['role'] == 0) {
            return json_encode(['resutlt' => 1, 'message' => '权限不足']);
        }
        $id = $request->input('id');
        $user = UserModels::where(['id' => $id])->get();
        return response()->json(['status' => 0, 'msg' => '查询成功！', 'data' => $user]);
    }

    //编辑操作
    public function update(Request $request)
    {
        $token = $request->header('token');
        $uid = $this->getPe($token);
        date_default_timezone_set('PRC'); //设置中国时区
        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        if ($uid['role'] == 0) {
            return json_encode(['resutlt' => 1, 'message' => '权限不足']);
        }
        $id = $request->input('id');
        $users = $request->except('_token');

        //进行验证
        $rule = [
            'enterprise' => 'required|between:4,18|unique:plc_user,enterprise,' . $id,
            'user_name'  => 'required|between:2,8',
            'phone'      => 'required|phone',
            'user_names' => 'required|between:2,8',
            'phones'     => 'required|phone',
            'account'    => 'required|between:4,18|unique:plc_user,enterprise,' . $id,
            'language'   => 'required',
            'notification_method' => 'required',
            'email'      => 'required_if:notification_method,1|email'
        ];

        $msg = [
            'enterprise.required' => '企业名必须输入',
            'enterprise.between' => '企业名长度必需在4-18位之间',
            'user_name.required' => '联系人1必须输入',
            'phone.required'     => '手机号不能为空',
            'phone.phone'     => '手机号不合法',
            'user_name.between' => '联系人1长度必需在2-8位之间',
            'user_names.required' => '联系人2必须输入',
            'phones.required'     => '手机号不能为空',
            'phones.phone'     => '手机号不合法',
            'user_names.between' => '联系人2长度必需在2-8位之间',
            'account.required' => '账号必须输入',
            'account.between' => '账号长度必需在4-18位之间',
            'account.unique' => '账号和原来一样/重复',
            'enterprise.unique' => '企业名称重复',
            'language.required' => '语言必须选择',
            'notification_method.required' => '通知方式必须选择',
            'email.required_if' => '当通知方式为邮件时，邮箱地址必须填写',
        ];
        $validator = Validator::make($users, $rule, $msg);
        if ($validator->fails()) {
            return response()->json(['status' => 1, 'msg' => '修改失败！', 'data' => $validator->errors()]);
        }

        $res = UserModels::where(['id' => $id])->update([
            'enterprise' => $users['enterprise'],
            'user_name' => $users['user_name'],
            'phone'     => $users['phone'],
            'user_names' => $users['user_names'],
            'phones' => $users['phones'],
            'account' => $users['account'],
            'status'    => $users['status'],
            'content' => $users['content'],
            'language' => $users['language'],
            'notification_method' => $users['notification_method'],
            'updated_at' => time(),
            'email' => $users['email'] ?? null
        ]);

        if ($res) {
            return response()->json(['status' => 0, 'msg' => '修改成功！']);
        } else {
            return response()->json(['status' => 1, 'msg' => '修改失败！']);
        }
    }

    //删除
    public function del(Request $request)
    {
        $token = $request->header('token');
        $uid = $this->getPe($token);
        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        if ($uid['role'] == 0) {
            return json_encode(['resutlt' => 1, 'message' => '权限不足']);
        }
        $input = $request->input('ids');
        foreach ($input as $v) {
            $res = UserModels::where('id', $v)->delete();
            DB::table('plc_device_user')->where('qiye_id', $v)->delete();
        }

        if ($res) {
            return response()->json(['status' => 0, 'msg' => '删除成功！']);
        } else {
            return response()->json(['status' => 1, 'msg' => '删除失败！']);
        }
    }

    //用户启用禁用
    public function state(Request $request)
    {
        $token = $request->header('token');
        $uid = $this->getPe($token);
        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        if ($uid['role'] == 0) {
            return json_encode(['resutlt' => 1, 'message' => '权限不足']);
        }
        $id = $request->input('id');
        $status = $request->input('status');
        $res = UserModels::where(['id' => $id])->update([
            'status'    => $status
        ]);
        if ($res) {
            return response()->json(['status' => 0, 'msg' => '修改成功！']);
        } else {
            return response()->json(['status' => 1, 'msg' => '修改失败！']);
        }
    }

    //重置密码
    public function chongzhi(Request $request)
    {
        $id = $request->input('id');
        $token = $request->header('token');
        $uid = $this->getPe($token);
        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        if ($uid['role'] != 1) {
            return json_encode(['resutlt' => 1, 'message' => '权限不足']);
        }
        $password = Crypt::encrypt('*********');
        $res = DB::table('plc_user')->where('id', $id)->update(['password' => $password]);
        if ($res) {
            return response()->json(['status' => 0, 'msg' => '密码重置成功！']);
        } else {
            return response()->json(['status' => 1, 'msg' => '密码重置失败！']);
        }
    }

    //修改语言
    public function language(Request $request)
    {
        $token = $request->header('token');
        $uid = $this->getPe($token);
        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        $language = $request->input('language');
        $res = UserModels::where(['id' => $uid])->update([
            'language'    => $language
        ]);
        if ($res) {
            return response()->json(['status' => 0, 'msg' => '修改成功！']);
        } else {
            return response()->json(['status' => 1, 'msg' => '修改失败！']);
        }
    }
}
