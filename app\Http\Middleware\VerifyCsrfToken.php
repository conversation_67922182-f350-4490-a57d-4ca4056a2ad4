<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array
     */
    protected $except = [
        //
       'login/store',
        'user/list',
        'user/index',
        'user/edit',
        'user/update',
        'user/store',
        'user/del',
        'device',
        'device/create',
        'device/edit',
        'device/update',
        'device/del',
        'index',
        'error/list',
        'admin/index',
        'admin/update',
        'device/store',
        'admin/updates',
        'user/state',
        'mqtt',
        'cesi',
        'fasong',
        'deatails/setUP',
        'deatails/errorSET',
        'deatails/index',
        'user/chongzhi',
        'deatails/setUPs',
        'device/details',
        'modbus',
        'clear',
        'message',
        'deviceEdit',
        'usagetime',
        'dingshi',
        'changyong',
        'setuperror',
        'chushi',
        'restart',
        'dingshifs',
        'overtime',
        'deviceError',
        'cesifasong',
        'Set',
        'dingshiout',
        'email'
    ];
}
