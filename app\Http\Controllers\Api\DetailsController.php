<?php


namespace App\Http\Controllers\Api;

use App\Http\Controllers\Api\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Validator;


class DetailsController extends Controller
{
    //设备详情主页面
    public function index(Request $request)
    {
        //获取token
        $token = $request->header('token');
//        dd($token);
        $uid = $this->getPe($token);
        $info = $request->all();
//        dd($uid);
        if(!$token)
        {
            return json_encode(['resutlt'=>1,'message'=>'请登录']);
        }
        // dd(time());
        // dd(date('Y').date('m').date('d'));
        $res = DB::table('plc_device_user as t1')->leftjoin('plc_device_content as t2','t1.id','=','t2.device_id')
                                                ->leftjoin('plc_device_restart as t3','t1.id','=','t3.d_id')
                                                ->leftjoin('plc_device_pattern as t4','t2.pattern_id','=','t4.id')
                                                ->where(['t1.id'=>$info['id'],'t3.time'=>$info['time']])
                                                ->select('t1.id','t1.device','t1.device_number','t1.address','t1.status','t2.testing_number','t2.current_use','t3.restart_number','t3.time','t4.pattern_content')
                                                ->get();
        // dd($res);
        $restart_total = DB::table('plc_device_restart')->where(['d_id'=>$info['id']])->select('restart_number')->get();
        $restart_total = objToArr($restart_total);
        $total[] ='';
        if($restart_total){
            foreach ($restart_total as $v){
                // $id = $v['id'];
                 $total[] = $v['restart_number'];
                    
            }
        }
        
            // dd($total);
       return response()->json(['status'=>0,'msg'=>'查询成功！','data'=>$res,'total'=>$total]);
    }


    //重启命令
    public function restart(Request $request)
    {

    }

    //
//    使用时间
    public function setUPs(Request $request)
    {
        //获取token
        $token = $request->header('token');
//        dd($token);
        $uid = $this->getPe($token);
        $id = $request->input('id');
        $info = $request->all();
//        dd($uid);
        //查询设备表是否存在该设备id
        $rose = DB::table('plc_device_user')->where(['id'=>$id])->get();
//        dd($rose);
        $rose = objToArr($rose);
        // dd($rose);
        if(!$rose)
        {
            return json_encode(['resutlt'=>1,'message'=>'该设备不存在']);
        }
        if(!$token)
        {
            return json_encode(['resutlt'=>1,'message'=>'请登录']);
        }
        // $date = $request->input('date');
//        dd($id);
        $d_id = DB::table('plc_date')->where(['d_id'=>$id])->get();
        $d_id = objToArr($d_id);
        $res = DB::table('plc_date')->where(['d_id'=>$id])->update([
            'monday'=>$info['monday'],
        'tuesday'=>$info['tuesday'],
        'wednesday'=>$info['wednesday'],
        'thursday'=>$info['thursday'],
        'friday'=>$info['friday'],
        'saturday'=>$info['saturday'],
        'sunday'=>$info['sunday']
        ]);
        // dd($res);
        if($res)
        {
            return json_encode(['resutlt'=>0,'message'=>'成功']);
        }else{
            return json_encode(['resutlt'=>1,'message'=>'更改失败']);
        }
    
    
    }
    
    public function setUP(Request $request)
    {
        //获取token
        $token = $request->header('token');
//        dd($token);
        $uid = $this->getPe($token);
        $id = $request->input('id');
        $info = $request->all();
//        dd($uid);
        //查询设备表是否存在该设备id
        $rose = DB::table('plc_device_user')->where(['id'=>$id])->get();
//        dd($rose);
        $rose = objToArr($rose);
        // dd($rose);
        if(!$rose)
        {
            return json_encode(['resutlt'=>1,'message'=>'该设备不存在']);
        }
        if(!$token)
        {
            return json_encode(['resutlt'=>1,'message'=>'请登录']);
        }
        $date = $request->input('date');
//        dd($id);
        $d_id = DB::table('plc_date')->where(['d_id'=>$id])->get();
        $d_id = objToArr($d_id);
        
        // dd($d_id);
        if($d_id==[])
        {
            $res = DB::table('plc_date')->insert([
                'd_id'=>$id,
                'monday'=>null,
                'tuesday'=>null,
                'wednesday'=>null,
                'thursday'=>null,
                'friday'=>null,
                'saturday'=>null,
                'sunday'=>null
            ]);
//            dd($res);
            $data = DB::table('plc_date')->where(['d_id'=>$id])->get();
            // dd($date);
            return response()->json(['status'=>0,'msg'=>'查询成功！','data'=>$data]);
            if($res)
            {
                return json_encode(['resutlt'=>0,'message'=>'成功']);
            }else{
                return json_encode(['resutlt'=>1,'message'=>'失败']);
            }

        }
        $data = DB::table('plc_date')->where(['d_id'=>$id])->get();
            // dd($date);
        return response()->json(['status'=>0,'msg'=>'查询成功！','data'=>$data]);
        // dd($info);
        
    }
    //报警设置
    public function errorSET(Request $request)
    {
        //获取token
        $token = $request->header('token');
//        dd($token);
        $uid = $this->getPe($token);
        $id = $request->input('id');
//        dd($uid);
        //查询设备表是否存在该设备id
        $rose = DB::table('plc_device_user')->where(['id'=>$id])->get();
//        dd($rose);
        $rose = objToArr($rose);
//        dd($rose);
        if(!$rose)
        {
            return json_encode(['resutlt'=>1,'message'=>'该设备不存在']);
        }
        if(!$token)
        {
            return json_encode(['resutlt'=>1,'message'=>'请登录']);
        }
    }
}