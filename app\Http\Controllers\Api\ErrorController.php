<?php


namespace App\Http\Controllers\Api;


use Illuminate\Http\Request;
use App\Http\Controllers\Api\Controller;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Home\CodeController;
use App\Services\AlertMessageService;

class ErrorController extends Controller
{
    
    //报警列表
    public function list(Request $request)
    {
        $info = $request->all();
        $token = $request->header('token');
        $uid = $this->getPe($token);
        $id = $uid['id'];
        if(!$token)
        {
            return json_encode(['resutlt'=>1,'message'=>'请登录']);
        }
        if(!empty($info['page'])){
            $page = $info['page'];
        }else{
            $page = 1;
        }
        
        $limit = empty($info['pagesize'])?10:$info['pagesize'];
        if($uid['role']==1){
            //获取所有报错信息
           $data = DB::table('plc_device_error as t1')
                ->select('t1.id','t2.device','t2.device_number','t2.qiye_id','t3.content_error','t4.enterprise','t4.enterprise_number','t4.user_name','t4.phone','t4.user_names','t4.phones','t1.created_at')
                ->leftjoin('plc_device_user as t2','t1.u_id','=','t2.id')
                ->leftjoin('plc_error_content as t3','t1.c_id','=','t3.id')
                ->leftjoin('plc_user as t4','t2.qiye_id','=','t4.id')
                ->where(['t2.del'=>null])
                ->where('t2.device', '!=', null)
                ->where(function ($query) use($request){
                    $where = $request->input('device_sousuo');
                    if(!empty($where)){
                        $query->where('device','like','%'.$where.'%');
                        $query->orwhere('device_number', 'like', '%'.$where.'%');
                    }
                })->where(function ($query) use($request){
                    $where = $request->input('qiye_sousuo');
                    if(!empty($where)){
                        $query->where('enterprise','like','%'.$where.'%');
                        $query->orwhere('enterprise_number', 'like', '%'.$where.'%');
                    }
                })->where(function ($query) use($request){
                    $where = $request->input('error_content');
                    if(!empty($where)){
                        $query->where('content_error','like','%'.$where.'%');
                    }
                })->orderBy('id','desc')->paginate($limit);
            $data = objToArr($data);

            // dd($data);
            if(!$data)
            {
                return response()->json(['status'=>1,'msg'=>'未查询到相关信息！']);
            }
            $errortotal = DB::table(DB::raw('(select u_id from plc_device_error group by u_id) as tab'))->count();
            foreach ($data['data'] as $k=>$v)
            {
                $data['data'][$k]['created_at'] = date('Y-m-d H:i:s',$v['created_at']);
            }
            $warn_device = DB::table(DB::raw('(select u_id from plc_device_error group by u_id) as tab'))->count();
            return response()->json(['status'=>0,'msg'=>'查询成功！','page'=>$data['current_page'],'last_page'=>$data['last_page'],'data'=>$data,'errortotal'=>$errortotal, 'warn_device' => $warn_device]);
        }
        $res = DB::table('plc_user')->where(['id'=>$id])->get();
        if(!$res)
        {
            return json_encode(['resutlt'=>1,'message'=>'该企业不存在']);
        }

        //获取所有报错信息
            
            $data = DB::table('plc_device_error as t1')
            ->select('t1.id','t2.device','t2.device_number','t2.qiye_id','t3.content_error','t4.enterprise','t4.enterprise_number','t4.user_name','t4.phone','t4.user_names','t4.phones','t1.created_at')
            ->leftjoin('plc_device_user as t2','t1.u_id','=','t2.id')
            ->leftjoin('plc_error_content as t3','t1.c_id','=','t3.id')
            ->leftjoin('plc_user as t4','t2.qiye_id','=','t4.id')
            ->where(['qiye_id'=>$id,'t2.del'=>null])
            ->where(function ($query) use($request){
                $where = $request->input('device_sousuo');
                if(!empty($where)){
                    $query->where('device','like','%'.$where.'%');
                    $query->orwhere('device_number', 'like', '%'.$where.'%');
                }
            })->where(function ($query) use($request){
                $where = $request->input('qiye_sousuo');
                if(!empty($where)){
                    $query->where('enterprise','like','%'.$where.'%');
                    $query->orwhere('enterprise_number', 'like', '%'.$where.'%');
                }
            })->where(function ($query) use($request){
                $where = $request->input('error_content');
                if(!empty($where)){
                    $query->where('content_error','like','%'.$where.'%');
                }
            })->orderBy('id','desc')->paginate($limit);
        
        $data = objToArr($data);
        
        if(!$data)
        {
            return response()->json(['status'=>1,'msg'=>'未查询到相关信息！']);
        }
        foreach ($data['data'] as $k=>$v)
        {
            $data['data'][$k]['created_at'] = date('Y-m-d H:i:s',$v['created_at']);
        }
        $errortotal = DB::table('plc_device_error')->groupBy('u_id')->count();
        return response()->json(['status'=>0,'msg'=>'查询成功！','page'=>$data['current_page'],'last_page'=>$data['last_page'],'data'=>$data,'errortotal'=>$errortotal]);
        
        
    }
    
    //监测设备问题并报警
    public function deviceError()
    {
        $data = DB::table('plc_device_user')->where(['del'=>null])->get();
        $data = objToArr($data);
        //清除3个月前的报警
        $three_month = strtotime('-3 month', time());
        DB::table('plc_device_error')->where('created_at', '<=', $three_month)->delete();
 
        $the = new CodeController;
        // $phone = '13337866669';
       
        foreach ($data as $k=>$v)
        {
            // 获取企业联系人信息（手机号和邮箱以及语言设置）
            $userInfo = DB::table('plc_user')->where('id', $v['qiye_id'])->where(['del'=>null])->select('phone', 'email', 'language')->first();
            $phone = $userInfo->phone ?? '';
            $email = $userInfo->email ?? '';
            $language = $userInfo->language ?? 'zh_CN';
            $devicename = $v['device'];
            $error = DB::table('plc_error_notice')->where('sn',$v['device_number'])->first();
            $error = objToArr($error);
            if(!empty($error))
            {
                //判断长时间未运行
                if(!empty($error['csjwyxbj']))
                {
                    $csjwyxbj = time() - $error['weiyunxingsjsz'] * 86400;
                    if($csjwyxbj>$v['time'])
                    {
                        $exist = DB::table('plc_device_error')->where(['u_id'=>$v['id'],'c_id'=>1])->first();
                        if(empty($exist)) {
                            $alertMessage = AlertMessageService::getAlertMessage('long_time_not_running', $language);
                            $notificationData = [
                                'phone' => $phone,
                                'email' => $email,
                                'devicename' => $devicename,
                                'contents' => $alertMessage
                            ];
                            $the->sendSmartNotification($notificationData);
                            DB::table('plc_device_error')->insertGetId(['u_id'=>$v['id'],'c_id'=>1,'created_at'=>time(),'updated_at'=>time()]);
                        }
                    }
                }
                //判断泵1液位
                if(!empty($error['leveldz']))
                {
                    $leveldz = DB::table('plc_device_details')->where('sn',$v['device_number'])->get();
                    $leveldz = objToArr($leveldz);
                    if(!empty($leveldz))
                    {
                        $leveldz = $leveldz[0];
                        if($error['leveldz']>=$leveldz['bsurplus'])
                        {
                            $exist = DB::table('plc_device_error')->where(['u_id'=>$v['id'],'c_id'=>2])->where('has_sovle', 0)->first();
                            if(empty($exist)) {
                                $alertMessage = AlertMessageService::getAlertMessage('pump1_level_alarm', $language);
                                $notificationData = [
                                    'phone' => $phone,
                                    'email' => $email,
                                    'devicename' => $devicename,
                                    'contents' => $alertMessage
                                ];
                                $the->sendSmartNotification($notificationData);

                                DB::table('plc_device_error')->insertGetId(['u_id'=>$v['id'],'c_id'=>2,'created_at'=>time(),'updated_at'=>time()]);
                            }
                        }
                    }

                }
                //判断泵2液位
                if(!empty($error['levelsdz']))
                {
                    // $csjwyxbj = time() - $error['csjwyxbj'];
                    $leveldz = DB::table('plc_device_details')->where('sn',$v['device_number'])->get();
                    $leveldz = objToArr($leveldz);
                    if(!empty($leveldz))
                    {
                        $leveldz = $leveldz[0];
                        if($error['levelsdz']>=$leveldz['bsurpluss'])
                        {
                            $exist = DB::table('plc_device_error')->where(['u_id'=>$v['id'],'c_id'=>3])->where('has_sovle', 0)->first();
                            if(empty($exist)) {
                                $alertMessage = AlertMessageService::getAlertMessage('pump2_level_alarm', $language);
                                $notificationData = [
                                    'phone' => $phone,
                                    'email' => $email,
                                    'devicename' => $devicename,
                                    'contents' => $alertMessage
                                ];
                                $the->sendSmartNotification($notificationData);
                                DB::table('plc_device_error')->insertGetId(['u_id'=>$v['id'],'c_id'=>3,'created_at'=>time(),'updated_at'=>time()]);
                            }
                        }
                    }

                }
            }
        }
    }
    
    
    
    
    
    
    
}