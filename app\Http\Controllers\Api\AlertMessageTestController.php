<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Api\Controller;
use Illuminate\Http\Request;
use App\Services\AlertMessageService;
use Illuminate\Support\Facades\DB;

class AlertMessageTestController extends Controller
{
    /**
     * 测试多语言报警消息
     */
    public function testAlertMessages(Request $request)
    {
        $language = $request->input('language', 'zh_CN');
        
        // 获取所有报警消息
        $messages = [
            'long_time_not_running' => AlertMessageService::getAlertMessage('long_time_not_running', $language),
            'pump1_level_alarm' => AlertMessageService::getAlertMessage('pump1_level_alarm', $language),
            'pump2_level_alarm' => AlertMessageService::getAlertMessage('pump2_level_alarm', $language),
            'water_pump_failure' => AlertMessageService::getAlertMessage('water_pump_failure', $language),
            'detection_count_exceeded' => AlertMessageService::getAlertMessage('detection_count_exceeded', $language, [
                'count' => 150,
                'limit' => 100
            ]),
            'device_offline' => AlertMessageService::getAlertMessage('device_offline', $language),
            'temperature_abnormal' => AlertMessageService::getAlertMessage('temperature_abnormal', $language),
            'pressure_abnormal' => AlertMessageService::getAlertMessage('pressure_abnormal', $language),
            'flow_abnormal' => AlertMessageService::getAlertMessage('flow_abnormal', $language),
            'voltage_abnormal' => AlertMessageService::getAlertMessage('voltage_abnormal', $language),
            'sensor_failure' => AlertMessageService::getAlertMessage('sensor_failure', $language),
            'communication_error' => AlertMessageService::getAlertMessage('communication_error', $language),
            'system_error' => AlertMessageService::getAlertMessage('system_error', $language)
        ];
        
        return response()->json([
            'success' => true,
            'language' => $language,
            'language_display_name' => AlertMessageService::getLanguageDisplayName($language),
            'messages' => $messages
        ]);
    }
    
    /**
     * 测试根据用户ID获取语言设置
     */
    public function testUserLanguage(Request $request)
    {
        $userId = $request->input('user_id');
        
        if (!$userId) {
            return response()->json([
                'success' => false,
                'message' => '请提供用户ID'
            ]);
        }
        
        $language = AlertMessageService::getUserLanguage($userId);
        $user = DB::table('plc_user')->where('id', $userId)->where('del', null)->first();
        
        return response()->json([
            'success' => true,
            'user_id' => $userId,
            'user_info' => $user,
            'detected_language' => $language,
            'language_display_name' => AlertMessageService::getLanguageDisplayName($language)
        ]);
    }
    
    /**
     * 测试根据设备号获取用户语言设置
     */
    public function testDeviceUserLanguage(Request $request)
    {
        $deviceNumber = $request->input('device_number');
        
        if (!$deviceNumber) {
            return response()->json([
                'success' => false,
                'message' => '请提供设备号'
            ]);
        }
        
        $language = AlertMessageService::getUserLanguageByDevice($deviceNumber);
        
        // 获取设备和用户信息
        $deviceInfo = DB::table('plc_user as t1')
            ->leftJoin('plc_device_user as t2', 't1.id', '=', 't2.qiye_id')
            ->where('t2.device_number', $deviceNumber)
            ->where('t1.del', null)
            ->where('t2.del', null)
            ->select('t1.id', 't1.enterprise', 't1.language', 't2.device', 't2.device_number')
            ->first();
        
        return response()->json([
            'success' => true,
            'device_number' => $deviceNumber,
            'device_info' => $deviceInfo,
            'detected_language' => $language,
            'language_display_name' => AlertMessageService::getLanguageDisplayName($language)
        ]);
    }
    
    /**
     * 获取支持的语言列表
     */
    public function getSupportedLanguages()
    {
        $languages = AlertMessageService::getSupportedLanguages();
        $languageList = [];
        
        foreach ($languages as $code) {
            $languageList[] = [
                'code' => $code,
                'display_name' => AlertMessageService::getLanguageDisplayName($code)
            ];
        }
        
        return response()->json([
            'success' => true,
            'supported_languages' => $languageList
        ]);
    }
    
    /**
     * 批量获取多语言消息
     */
    public function getBatchMessages(Request $request)
    {
        $language = $request->input('language', 'zh_CN');
        $messageKeys = $request->input('message_keys', []);
        
        if (empty($messageKeys)) {
            $messageKeys = [
                'long_time_not_running',
                'pump1_level_alarm',
                'pump2_level_alarm',
                'water_pump_failure',
                'detection_count_exceeded'
            ];
        }
        
        $messages = AlertMessageService::getBatchMessages($messageKeys, $language);
        
        return response()->json([
            'success' => true,
            'language' => $language,
            'messages' => $messages
        ]);
    }
}
