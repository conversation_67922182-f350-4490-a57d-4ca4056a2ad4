<?php

namespace App\Http\Controllers\Api;

use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Http\Request;
use App\Http\Controllers\Api\Controller;

use Illuminate\Support\Facades\DB;


class LoginController extends Controller
{
    public function login()
    {
        //        return view('admin.login');
    }
    public function store(Request $request)
    {
        $input = $request->all();
        $input = $request->except('_token');
        //验证规则
        $rule = [
            'username' => 'required|between:4,18',
            'password' => 'required|between:6,16'
        ];

        //返回错误
        $msg = [
            'username.required' => '用户名必须输入',
            'username.between' => '用户名长度必需在4-18位之间',
            'password.required' => '密码必须输入',
        ];
        $validator = Validator::make($input, $rule, $msg);
        if ($validator->fails()) {
            return response()->json(['status' => 0, 'msg' => '登录失败！', 'data' => $validator->errors()]);
        }

        $admin = DB::table('plc_admin')->where('account', $input['username'])->first();

        $admin = objToArr($admin);
        //判断用户名是否存在
        if (empty($admin)) {

            //判断普通用户是否存在
            $admin = DB::table('plc_user')->where(['account' => $input['username'], 'del' => null])->first();
            $admin = objToArr($admin);
            //            dd($admin);
            if (!$admin) {
                return response()->json(['status' => 0, 'msg' => '用户名错误！']);
            }
            //判断普通用户密码是否正确
            if ($input['password'] != Crypt::decrypt($admin['password'])) {
                return response()->json(['status' => 1, 'msg' => '密码错误！']);
            }
            //判断账户是否启用
            if ($admin['status'] == 1) {
                return  response()->json(['status' => 1, 'msg' => '账户未启用，请联系管理员！']);
            }
            $token = encrypts(strval($admin['id']));
            $tokens = encrypts($admin['id'] . time());
            Redis::set($token, $tokens);
            Redis::expire($token, 3600 * 24 * 15);

            if (!$token) {
                return static::error("账号或密码错误", 4001);
            }
            $admin = DB::table('plc_user')->where(['account' => $input['username']])->get();
            $admin = objToArr($admin);
            if ($admin[0]['one'] == '0') {
                DB::table('plc_user')->where(['account' => $input['username']])->update(['one' => 1]);
                return response()->json(['status' => 0, 'msg' => '登录成功,请修改密码！', 'token' => $tokens, 'role' => $admin[0]['role'], 'language' => $admin[0]['language']]);
            }
            return response()->json(['status' => 0, 'msg' => '登录成功！', 'token' => $tokens, 'role' => $admin[0]['role'], 'language' => $admin[0]['language']]);

        }

        //判断密码是否正确
        if ($input['password'] != Crypt::decrypt($admin['password'])) {
            return response()->json(['status' => 1, 'msg' => '密码错误！']);
        }

        $token = encrypts(strval($admin['sort']));

        $tokens = encrypts($admin['sort'] . time());

        Redis::set($token, $tokens);

        Redis::expire($token, 3600 * 24 * 15);
        if (!$token) {
            return static::error("账号或密码错误", 4001);
        }

        return response()->json(['status' => 0, 'msg' => '登录成功！', 'token' => $tokens, 'role' => $admin['role']]);
    }

    //退出登录
    public function logout(Request $request)
    {
        $token = $request->header('token');
        $token = '';

        return response()->json(['status' => 0, 'msg' => '退出成功！', 'token' => $token]);
    }
}
